{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
      "^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue",
      "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"
    }
  },
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        // #ifdef MP-TOUTIAO
        "navigationBarBackgroundColor": "#1D64D9",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES",
          "pullRefresh": false,
          "allowsBounceVertical": "NO"
        }
      }
    },
    
    {
      "path": "pages/enterprise/index",
      "style": {
        // #ifdef MP-WEIXIN || MP-TOUTIAO
        "navigationBarTitleText": "企业",
        // #endif
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        // #ifdef MP-TOUTIAO
        "navigationBarBackgroundColor": "#1D64D9",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/message/index",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#1D64D9",
        "mp-alipay": {
          "pullRefresh": false,
          "allowsBounceVertical": "NO"
        }
      }
    },
    {
      "path": "pages/message/messageSearch",
      "style": {
        "navigationBarTitleText": "搜索聊天信息",
        "navigationBarBackgroundColor": "#fff"
      }
    },
    {
      "path": "pages/message/pages/noticeIndex",
      "style": {
        "navigationBarTitleText": "通知",
        "navigationBarBackgroundColor": "#fff"
      }
    },
    {
      "path": "pages/message/chatDetails",
      "style": {
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#fff",
        "mp-alipay": {
          "pullRefresh": false,
          "allowsBounceVertical": "NO"
        }
      }
    },
    {
      "path": "pages/message/noticeTypeList",
      "style": {
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#fff"
      }
    },
    {
      "path": "pages/message/noticeDetail",
      "style": {
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#fff"
      }
    },
    {
      "path": "pages/message/onceBrowse",
      "style": {
        "navigationBarTitleText": "谁看过我",
        "navigationBarBackgroundColor": "#fff"
      }
    },
    {
      "path": "pages/message/deliveryRecord",
      "style": {
        "navigationBarTitleText": "投递记录",
        "navigationBarBackgroundColor": "#fff",
        "mp-alipay": {
          "pullRefresh": false,
          "allowsBounceVertical": "NO"
        }
      }
    },
    {
      "path": "pages/message/browseRecords",
      "style": {
        "navigationBarTitleText": "浏览记录",
        "navigationBarBackgroundColor": "#fff"
      }
    },
    {
      "path": "pages/message/deliveryDetails",
      "style": {
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#FFF"
      }
    },
    {
      "path": "pages/my/index",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/position/index",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/web-view/index",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/job/company",
      "style": {
        // #ifdef MP-WEIXIN || MP-TOUTIAO || MP-ALIPAY
        "navigationBarTitleText": "公司详情",
        // #endif
        "navigationBarBackgroundColor": "#34536E",
        "navigationBarTextStyle": "white",
        // #ifdef MP-WEIXIN
        "navigationStyle": "custom"
        // #endif
      }
    },
    {
      "path": "pages/job/position",
      "style": {
        // #ifdef MP-WEIXIN || MP-TOUTIAO || MP-ALIPAY
        "navigationBarTitleText": "职位详情",
        // #endif
        // #ifdef MP-WEIXIN
        "navigationStyle": "custom"
        // #endif
      }
    },
    {
      "path": "pages/job/deliveryResult",
      "style": {
        "navigationBarTitleText": "投递结果"
      }
    },
    {
      "path": "pages/sydw/index",
      "style": {
        // #ifdef MP-WEIXIN || MP-TOUTIAO
        "navigationBarTitleText": "事业单位",
        // #endif
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/sydw/article",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/sydw/search",
      "style": {
        "navigationBarTitleText": "文章搜索"
      }
    },
    {
      "path": "pages/index/classify",
      "style": {
        "navigationBarTitleText": "职位/行业分类"
      }
    },
    {
      "path": "pages/index/city",
      "style": {
        "navigationBarTitleText": "切换城市"
      }
    },
    {
      "path": "pages/position/result",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/unit/index",
      "style": {
        "navigationBarTitleText": "选择地域"
      }
    },
    {
      "path": "pages/setting/company",
      "style": {
        "navigationBarTitleText": "屏蔽公司",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/setting/addEnter",
      "style": {
        "navigationBarTitleText": "添加屏蔽"
      }
    },
    {
      "path": "pages/jobFair/jobFairIndexs",
      "style": {
        "navigationBarTitleText": "招聘会"
      }
    },
    {
      "path": "pages/jobFair/jobFairDetail",
      "style": {
        "navigationBarTitleText": "",
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/jobFair/positionDetail",
      "style": {
        "navigationBarTitleText": "",
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/jobFair/shareCode",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/pay/wxpay",
      "style": {
        "navigationBarTitleText": "微信支付"
      }
    },
    {
      "path": "pages/map/index",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/gxrc/enterprise/enterprise",
      "style": {
        "navigationBarTitleText": "企业详情"
      }
    },
    {
      "path": "pages/gxrc/position/position",
      "style": {
        "navigationBarTitleText": "职位详情"
      }
    },
    {
      "path": "pages/vip/index",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/redirect/index",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/redirect/contact",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/redirect/service",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/redirect/login",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom",
        // #endif
        "mp-alipay": {
          "transparentTitle": "always",
          "titlePenetrate": "YES"
        }
      }
    },
    {
      "path": "pages/job/positionPosterview",
      "style": {
        "navigationBarTitleText": "职位分享图"
      }
    },
    {
      "path": "pages/web-view/login",
      "style": {
        // #ifdef MP-WEIXIN || MP-ALIPAY
        "navigationStyle": "custom"
        // #endif
      }
    }
  ],
  "subPackages": [
    {
      "root": "pageEcharts",
      "pages": [
        {
          "path": "message/sumRecommend",
          "style": {
            "navigationBarTitleText": "综合推荐"
          }
        }
      ]
    },
    {
      "root": "pageExtraMessage",
      "pages": [
        {
          "path": "page/phrasesAdd",
          "style": {
               // #ifdef MP-WEIXIN || MP-ALIPAY
               "navigationStyle": "custom"
               // #endif
          }
        },
        {
          "path": "page/phrasesCustomize",
          "style": {
               // #ifdef MP-WEIXIN || MP-ALIPAY
               "navigationStyle": "custom"
               // #endif
          }
        },
        {
          "path": "page/phrasesMange",
          "style": {
               // #ifdef MP-WEIXIN || MP-ALIPAY
               "navigationStyle": "custom"
               // #endif
          }
        },
        {
          "path": "page/newGreetings",
          "style": {
               // #ifdef MP-WEIXIN || MP-ALIPAY
               "navigationStyle": "custom"
               // #endif
          }
        }
      ]
    },
    {
      "root": "pagelive",
      "pages": [
        {
          "path": "live/live-index",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        }
      ]
    },
    {
      "root": "mypages",
      "pages": [
        {
          "path": "my/photo/PhotoManage",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            },
            "navigationBarTitleText": "头像形象"
          }
        },
        {
          "path": "my/resume/baseInfo",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/tags",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/index",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/preview",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/shareResume",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            // #ifdef MP-WEIXIN || MP-TOUTIAO
            "navigationBarTitleText": "简历分享",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/careerObjective",
          "style": {
            // #ifdef MP-WEIXIN || MP-TOUTIAO
            "navigationBarTitleText": "管理求职意向(0/3)",
            // #endif
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/careerPosAndIns",
          "style": {
            // #ifdef MP-WEIXIN || MP-TOUTIAO
            "navigationBarTitleText": "求职意向",
            // #endif
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/work",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/resumeAnnexes",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#4F90F2"
          }
        },
        {
          "path": "my/resume/resumeAnalysis",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#4F90F2"
          }
        },
        {
          "path": "my/resume/uploadAnnexes",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#F7F9FC"
          }
        },
        {
          "path": "my/collection/collectionIndex",
          "style": {
            "navigationBarTitleText": "我的收藏"
          }
        },
        {
          "path": "my/support/helpAndSupport",
          "style": {
            "navigationBarTitleText": "帮助和反馈"
          }
        },
        {
          "path": "my/support/editHelp",
          "style": {
            "navigationBarTitleText": "意见反馈"
          }
        },
        {
          "path": "my/support/replyDetails",
          "style": {
            "navigationBarTitleText": "意见反馈"
          }
        },
        {
          "path": "my/resume/project",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/education",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/educationpractice",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/train",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/certificate",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/language",
          "style": {
            "navigationBarTitleText": "语言能力"
          }
        },
        {
          "path": "my/resume/technology",
          "style": {
            "navigationBarTitleText": "技术能力"
          }
        },
        {
          "path": "my/resume/description",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/resume/otherSkill",
          "style": {
            "navigationBarTitleText": "其他技能"
          }
        },
        {
          "path": "my/resume/resumeOptimize",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/o2o/apply",
          "style": {
            "navigationBarTitleText": "我的招聘会(投递/预约)"
          }
        },
        {
          "path": "my/address/index",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/address/edit",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/support/jubao",
          "style": {
            "navigationBarTitleText": "失信举报"
          }
        },
        {
          "path": "my/competitiveness/competitiveness",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/activity/tickets",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/activity/school-tickets",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "my/activity/new-tickets",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "register/index",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "register/step2",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "register/step3",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "register/step4",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/baseInfo",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/identityInfo",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/work1FirstTime",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/work2Position",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/work3Company",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/work4CompanyTime",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/work5Skill",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/work6Description",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/internship1FirstTime",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/internship2Position",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/internship3Company",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/internship4CompanyTime",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/internship5Skill",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/internship6Description",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/education1Degree",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/education2School",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/education3Major",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/education4SchoolTime",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/career1Work",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/career2Salary",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/career3Description",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "registerBlue/picture",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "interview/index",
          "style": {
            "navigationBarTitleText": "面试记录"
          }
        },
        {
          "path": "interview/detail",
          "style": {
            "navigationBarTitleText": "面试详情",
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom"
            // #endif
          }
        }
      ]
    },
    {
      "root": "pageSchool",
      "pages": [
        {
          "path": "policy/page/policy-service",
          "style": {
             "navigationStyle": "custom"
          }
        },
        {
          "path": "policy/page/policy-manual",
          "style": {
            "navigationBarTitleText": "政策服务手册"
          }
        },
        {
          "path": "policy/page/policy-city",
          "style": {
            "navigationBarTitleText": "市县政策"
          }
        },
        {
          "path": "policy/page/policy-city-download",
          "style": {
            "navigationBarTitleText": "市县政策"
          }
        },
        {
          "path": "policy/page/policy-guidance",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "school/jobFairList/index",
          "style": {
            "navigationBarTitleText": "校园招聘会"
          }
        },
        {
          "path": "school/jobFairList/search",
          "style": {
            "navigationBarTitleText": "校园招聘会"
          }
        },
        {
          "path": "school/jobFairList/jobFairDetail",
          "style": {
            "navigationBarTitleText": "校园招聘会"
          }
        },
        {
          "path": "school/jobFairList/positionDetail",
          "style": {
            "navigationBarTitleText": "校园招聘会"
          }
        },
        {
          "path": "school/shareCode",
          "style": {
            "navigationBarTitleText": "校园招聘会海报",
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "school/information/index",
          "style": {
            "navigationBarTitleText": "就业资讯"
          }
        },
        {
          "path": "school/information/search",
          "style": {
            "navigationBarTitleText": "就业资讯"
          }
        },
        {
          "path": "school/information/article",
          "style": {
            "navigationBarTitleText": "就业资讯"
          }
        },
        {
          "path": "school/liveTelecast/index",
          "style": {
            "navigationBarTitleText": "热门直播"
          }
        },
        {
          "path": "school/liveTelecast/liveDetail",
          "style": {
            "navigationBarTitleText": "直播详情"
          }
        },
        {
          "path": "school/index",
          "style": {
            "navigationBarTitleText": "职达校园"
          }
        },
        {
          "path": "school/ResumePrinting/index",
          "style": {
            "navigationBarTitleText": "简历预约打印"
          }
        },
        {
          "path": "smdj/index",
          "style": {
            "navigationBarTitleText": "毕业生平台实名登记"
          }
        },
        {
          "path": "school/guidance/index",
          "style": {
            "navigationBarTitleText": "就业指导"
          }
        },
        {
          "path": "school/guidance/detail",
          "style": {
            "navigationBarTitleText": "就业指导详情",
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        },
        {
          "path": "entrepreneurship/page/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "entrepreneurship/page/article",
          "style": {
            "navigationBarTitleText": ""
          }
        }
      ],
      "plugins": {
        "polyv-player": {
          "version": "1.9.1",
          "provider": "wx4a350a258a6f7876"
        }
      }
    },
    {
      "root": "pageEvaluation",
      "pages": [
        {
          "path": "detail/personality-index",
          "style": {
            "navigationBarTitleText": "性格测评-直觉判断"
          }
        },
        {
          "path": "index/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "result/index",
          "style": {
            "navigationBarTitleText": "性格测评结果"
          }
        }
      ]
    },
    {
      "root": "pageEstatePark",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/famousEnt",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/jobFair",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/parkHire",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/policyGuidance",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/policyArticle",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/jobFairArticle",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "TopicPages/parkIntroductionDm",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "TopicPages/parkIntroductionBdxq",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "TopicPages/parkIntroductionMlxy",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "TopicPages/parkIntroductionFs",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/universityInfor",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/universityDetails",
          "style": {
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {

      "root": "pageToutiao",
      "pages": [
        {
          "path": "toutiao/index",
          "style": {
            "navigationBarBackgroundColor": "#1D64D9"
          }
        }
      ]
    },
    {
      "root": "pageService",
      "pages": [
        {
          "path": "salarySearch/index",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "salarySearch/detail",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "salarySearch/city",
          "style": {
            "navigationBarTitleText": "选择城市"
          }
        },
        {
          "path": "resumeMaking/index",
          "style": {
            "navigationBarTitleText": "简历制作"
          }
        }
      ]
    },
    {
      "root": "pageEnterprise",
      "pages": [
        {
          "path": "talent/index",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "talent/resume-list",
          "style": {
            "navigationBarTitleText": "简历列表",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "talent/resume-detail",
          "style": {
            "navigationBarTitleText": "简历详情",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "talent/talent-search",
          "style": {
            "navigationBarTitleText": "人才搜索",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "position/index",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "position/publish/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/publish/normalFirst",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/publish/normalSecond",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/publish/graduateSecound",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/sort",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/preview",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/selectPositionKeyWord",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/positionDetail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/selectMajor",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/selectPositionWelfareNames",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/publish/editPosirionName",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/editPositionDesc",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/positionTypeSelect",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/workAddressSelect",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/aiRunSe",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/publish/graduateFirst",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/position-list",
          "style": {
            "navigationBarTitleText": "职位列表",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "position/position-edit",
          "style": {
            "navigationBarTitleText": "编辑职位",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "position/position-publish",
          "style": {
            "navigationBarTitleText": "发布职位",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "message/index",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "message/chat-list",
          "style": {
            "navigationBarTitleText": "聊天列表",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "message/chat-detail",
          "style": {
            "navigationBarTitleText": "聊天详情",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "profile/index",
          "style": {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            "navigationStyle": "custom",
            // #endif
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "profile/company-info",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "企业信息",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "profile/edit-basic-info",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "企业基本信息",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "profile/edit-contact-info",
          "style": {
            "navigationBarTitleText": "联系方式",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "profile/edit-description",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "企业简介",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "profile/settings",
          "style": {
            "navigationBarTitleText": "设置",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "login/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "login/account",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "resume/manage",
          "style": {
            "navigationBarTitleText": "简历管理",
            "navigationBarBackgroundColor": "#ffffff",
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "resume/favorite",
          "style": {
            "navigationBarTitleText": "简历收藏夹",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "resume/recent",
          "style": {
            "navigationBarTitleText": "最近查看简历",
            "navigationBarBackgroundColor": "#ffffff",
            "onReachBottomDistance": 50,
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "resume/detail",
          "style": {
            "navigationBarTitleText": "简历详情",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "resume/favorite-detail",
          "style": {
            "navigationBarTitleText": "收藏夹详情",
            "navigationBarBackgroundColor": "#ffffff",
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "resume/search",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "resume/search-result",
          "style": {
            "navigationStyle": "custom",
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "resume/position-resumes",
          "style": {
            "navigationBarTitleText": "职位简历管理",
            "navigationBarBackgroundColor": "#ffffff",
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "resume/note",
          "style": {
            "navigationBarTitleText": "简历备注",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "profile/switchJobseeker",
          "style": {
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "preloadRule": {
    "pages/my/index": {
      "network": "all",
      "packages": ["mypages"]
    }
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#1971E8",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/img/tarbar/<EMAIL>",
        "selectedIconPath": "static/img/tarbar/<EMAIL>",
        "text": "职位"
      },
      {
        "pagePath": "pages/enterprise/index",
        "iconPath": "static/img/tarbar/<EMAIL>",
        "selectedIconPath": "static/img/tarbar/<EMAIL>",
        "text": "企业"
      },
      {
        "pagePath": "pages/message/index",
        "iconPath": "static/img/tarbar/<EMAIL>",
        "selectedIconPath": "static/img/tarbar/<EMAIL>",
        "text": "消息"
      },
      {
        "pagePath": "pages/my/index",
        "iconPath": "static/img/tarbar/<EMAIL>",
        "selectedIconPath": "static/img/tarbar/<EMAIL>",
        "text": "我的"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    // #ifdef MP-WEIXIN || MP-TOUTIAO
    "navigationBarTitleText": "广西人才网",
    // #endif
    // #ifdef MP-ALIPAY
    "navigationBarTitleText": "",
    // #endif
    "navigationBarBackgroundColor": "#ffffff",
    // #ifdef MP-WEIXIN
    "backgroundColor": "#F8F8F8",
    // #endif
    // #ifdef MP-TOUTIAO || MP-ALIPAY
    "backgroundColor": "#ffffff"
    // #endif
  },
  "condition": {
    //模式配置，仅开发期间生效
    "current": 0, //当前激活的模式（list 的索引项）
    "list": [
      {
        "name": "baseInfo",
        "path": "pages/my/resume/baseInfo"
      }
    ]
  },
  "navigateToMiniProgramAppIdList": ["wx5e63c00c60d7e8a7"]
}
