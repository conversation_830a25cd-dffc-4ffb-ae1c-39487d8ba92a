<template>
  <enterprise-layout
    title="我的"
    :current-tab="'profile'"
    :show-tab-bar="true"
    :show-nav-bar="false"
    @tab-change="handleTabChange"
  >
    <view class="page">
      <!-- 头部导航 -->
      <view class="header" :style="{ top: statusBarHeight + 'px', width: navWidth }">
        <view class="header-left">
          <view class="switch-identity" @tap="switchJobseeker">
            <image
              class="switch-icon"
              src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
            />
            <text class="switch-text">切换身份</text>
          </view>
        </view>
        <view class="header-right">
          <image
            class="header-icon"
            src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
          />
        </view>
      </view>

      <view class="pt-160rpx">
        <!-- 企业信息卡片 -->
        <view class="company-info">
          <image class="company-logo" :src="companyInfo?.logoUrl" />
          <view class="company-details">
            <text class="company-name">{{ companyInfo?.enterpriseName }}</text>
            <text class="service-period">服务期限 {{ companyInfo?.recruitTime }}</text>
          </view>
        </view>

        <!-- 主要功能菜单 -->
        <view class="main-menu">
          <view class="menu-grid">
            <view class="menu-item" @tap="goToCompanyInfo">
              <image
                class="menu-icon"
                src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
              />
              <text class="menu-text">企业信息</text>
              <view class="status-badge" v-if="!companyInfo?.isCompletionDescription">待完善</view>
            </view>
            <view class="menu-item">
              <image
                class="menu-icon"
                src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
              />
              <text class="menu-text">简历收藏</text>
            </view>
            <view class="menu-item">
              <image
                class="menu-icon"
                src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
              />
              <text class="menu-text">简历管理</text>
            </view>
            <view class="menu-item">
              <image
                class="menu-icon"
                src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
              />
              <text class="menu-text">面试管理</text>
            </view>
            <view class="menu-item">
              <image
                class="menu-icon"
                src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
              />
              <text class="menu-text">招聘会</text>
            </view>
          </view>
        </view>

        <!-- 营销横幅 -->
        <!-- <view class="promotion-banner">
          <image
            class="banner-image"
            src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGcfa3829fd77acfaceb94d1dcd5d21ccd.png"
          />
        </view> -->
        <!-- 其他服务区域 -->
        <view class="other-services">
          <view class="section-title">
            <text class="title-text">其他服务</text>
          </view>

          <!-- 第一行服务 -->
          <view class="services-grid">
            <view class="service-item">
              <image
                class="service-icon"
                src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
              />
              <text class="service-text">资质认证</text>
              <view class="cert-badge" v-if="auditStateText">{{ auditStateText }}</view>
            </view>
            <view class="service-item">
              <image
                class="service-icon"
                src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
              />
              <text class="service-text">我的资产</text>
            </view>

            <view class="service-item">
              <image
                class="service-icon"
                src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
              />
              <text class="service-text">账号绑定</text>
            </view>
            <view class="service-item" @tap="goToFeedback">
              <image
                class="service-icon"
                src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
              />
              <text class="service-text">意见反馈</text>
            </view>
            <view class="service-item" @tap="goToAbout">
              <image
                class="service-icon"
                src="https://image.gxrc.com/thirdParty/gxjy/miniprogram/my/<EMAIL>"
              />
              <text class="service-text">关于我们</text>
            </view>
          </view>
        </view>
        <!-- 底部信息 -->
        <view class="footer-info">
          <text class="footer-text"> 招聘求职服务/监督热线 400-0771-056 </text>
          <text class="footer-text"> 人力资源和社会保障咨询监督投诉电话 12333 </text>
          <text class="footer-text"> 资质证明 ｜桂ICP备16010643号-7A </text>
        </view>
      </view>
    </view>
  </enterprise-layout>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'
  import EnterpriseLayout from '../components/enterprise-layout.vue'
  import SkeletonLoader from '../components/skeleton-loader.vue'
  import { usePagePreload } from '../composables/usePagePreload'
  import { useEnterpriseAuth } from '../composables/useEnterpriseAuth'
  import { profileDataConfigs } from '../utils/data-configs'
  import { Enterprise } from '../services/Enterprise'
  import { EnterpriseBaseModel } from '../services/data-contracts'

  const { requireAuth } = useEnterpriseAuth()

  onLoad(async (options) => {
    // 检查企业端登录状态
    if (!requireAuth(options)) {
      return // 未登录会自动跳转到登录页面
    }
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight || 0
    const menuButton = uni.getMenuButtonBoundingClientRect()
    navWidth.value = menuButton.left - 10 + 'px'

    await getEnterpriseBaseInfo()
  })

  const statusBarHeight = ref(0)
  const navWidth = ref('')
  const companyInfo = ref<EnterpriseBaseModel>({})
  const getEnterpriseBaseInfo = async () => {
    const res = await Enterprise.apiEnterpriseGetbaseinfoGet()
    
    if (res.code == 1) {
      companyInfo.value = res.data
    }
  }

  const auditStateText = computed(() => {
    let text = ''
    switch(companyInfo.value?.enterpriseMaterialAuditState){
      case 0:
      case 2:
      case 3: 
        text = '待认证'
        break
      case 4:
        text = '待审核'
        break
      case 5:
        text = '审核不通过'
        break
      default:
        text = ''
        break
    }
    return text
  })
  // 页面方法
  const handleTabChange = (key: string) => {
    console.log('Tab changed to:', key)
  }

  const editCompanyInfo = () => {
    uni.navigateTo({
      url: '/pageEnterprise/profile/company-info'
    })
  }

  const goToCompanyInfo = () => {
    uni.navigateTo({
      url: '/pageEnterprise/profile/company-info'
    })
  }

  const goToRecruitmentData = () => {
    uni.navigateTo({
      url: '/pageEnterprise/profile/recruitment-data'
    })
  }

  const goToMemberCenter = () => {
    uni.navigateTo({
      url: '/pageEnterprise/profile/member-center'
    })
  }

  const goToHelp = () => {
    uni.navigateTo({
      url: '/pageEnterprise/profile/help'
    })
  }

  const goToFeedback = () => {
    uni.navigateTo({
      url: '/pageEnterprise/profile/feedback'
    })
  }

  const goToAbout = () => {
    uni.navigateTo({
      url: '/pageEnterprise/profile/about'
    })
  }

  const publishPosition = () => {
    uni.navigateTo({
      url: '/pageEnterprise/position/position-publish'
    })
  }

  const searchTalent = () => {
    uni.navigateTo({
      url: '/pageEnterprise/talent/talent-search'
    })
  }

  const viewAnalytics = () => {
    uni.navigateTo({
      url: '/pageEnterprise/position/analytics'
    })
  }

  const contactService = () => {
    uni.makePhoneCall({
      phoneNumber: '************'
    })
  }

  const switchJobseeker = () => {
    uni.navigateTo({
      url: '/pageEnterprise/profile/switchJobseeker'
    })
  }
  const logout = () => {
    uni.showModal({
      title: '退出登录',
      content: '确定要退出当前账户吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录状态
          uni.removeStorageSync('enterprise_token')

          // 跳转到登录页
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }
      }
    })
  }
</script>

<style lang="scss" scoped>
  // 页面整体样式
  .page {
    padding: 0;
    background: url(https://image.gxrc.com/thirdParty/gxjy/miniprogram/<EMAIL>) no-repeat
      center top;
    background-size: 100% 292rpx;
  }

  // 头部导航
  .header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 12rpx;
    background-color: transparent;
    position: fixed;
    left: 0;
    right: 0;
    z-index: 999;
    .header-left {
      .switch-identity {
        display: flex;
        align-items: center;
        background: #fff;
        padding: 10rpx 16rpx;
        border-radius: 28rpx;
        border: 2rpx solid #f2f3f8;
        margin-right: 20rpx;
        .switch-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
        }

        .switch-text {
          font-size: 24rpx;
          color: #58616d;
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .header-icon {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }

  // 企业信息卡片
  .company-info {
    display: flex;
    align-items: center;
    margin: 20rpx 30rpx;
    .company-logo {
      width: 96rpx;
      height: 96rpx;
      border-radius: 60rpx;
      margin-right: 24rpx;
    }

    .company-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .company-name {
        font-size: 40rpx;
        color: #333;
        margin-bottom: 12rpx;
      }
      .service-period {
        font-size: 22rpx;
        color: #6a6d72;
      }
    }
  }

  // 会员状态卡片
  .member-card {
    margin: 20rpx 32rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .member-info {
      display: flex;
      align-items: center;
      padding: 24rpx 32rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

      .member-icon {
        width: 80rpx;
        height: 80rpx;
        margin-right: 24rpx;
      }

      .member-details {
        flex: 1;

        .member-status {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;

          .vip-icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 12rpx;
          }

          .member-type {
            font-size: 28rpx;
            color: #fff;
            font-weight: 500;
          }
        }

        .remaining-days {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .upgrade-btn {
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 40rpx;
        padding: 16rpx 32rpx;

        .upgrade-text {
          font-size: 24rpx;
          color: #fff;
        }
      }
    }
  }

  // 主要功能菜单
  .main-menu {
    margin: 20rpx 10rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .menu-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 32rpx;

      .menu-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        .menu-icon {
          width: 60rpx;
          height: 60rpx;
          margin-bottom: 16rpx;
        }

        .menu-text {
          font-size: 24rpx;
          color: #333;
          text-align: center;
        }

        .status-badge {
          position: absolute;
          top: -12rpx;
          right: -24rpx;
          color: #fff;
          font-size: 20rpx;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
          transform: scale(0.8);
          background: #ff7802;
          border-radius: 12rpx 12rpx 12rpx 2rpx;
          border: 2rpx solid #ffffff;
        }
      }
    }
  }

  // 营销横幅
  .promotion-banner {
    margin: 20rpx 32rpx;
    border-radius: 16rpx;
    overflow: hidden;

    .banner-image {
      width: 100%;
      height: 160rpx;
      display: block;
    }
  }

  // 其他服务区域
  .other-services {
    margin: 20rpx 10rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .section-title {
      margin-bottom: 32rpx;

      .title-text {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .services-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 32rpx;
      margin-bottom: 40rpx;

      .service-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        .service-icon {
          width: 56rpx;
          height: 56rpx;
          margin-bottom: 16rpx;
        }

        .service-text {
          font-size: 24rpx;
          color: #333;
          text-align: center;
        }

        .cert-badge {
          position: absolute;
          top: -12rpx;
          right: -24rpx;
          color: #fff;
          font-size: 20rpx;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
          transform: scale(0.8);
          background: #ff7802;
          border-radius: 12rpx 12rpx 12rpx 2rpx;
          border: 2rpx solid #ffffff;
        }
      }
    }

    .services-grid-bottom {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 32rpx;
      justify-items: center;

      .service-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .service-icon {
          width: 80rpx;
          height: 80rpx;
          margin-bottom: 16rpx;
        }

        .service-text {
          font-size: 24rpx;
          color: #333;
          text-align: center;
        }
      }
    }
  }

  .footer-info {
    padding-top: 24rpx;
    text-align: center;
    .footer-text {
      display: block;
      font-size: 22rpx;
      color: #999;
      line-height: 1.6;
      margin-bottom: 8rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
</style>
